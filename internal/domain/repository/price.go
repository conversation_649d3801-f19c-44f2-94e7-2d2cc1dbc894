package repository

import (
	"context"

	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/shopspring/decimal"
)

// TODO 内存缓存更新逻辑补充
type PriceRepository interface {
	GetIndexAndMarkPrice(ctx context.Context, symbol string) (decimal.Decimal, decimal.Decimal)

	GetAllMarkPrice(ctx context.Context) cmap.ConcurrentMap[string, decimal.Decimal]
	GetMarkPrice(ctx context.Context, symbol string) decimal.Decimal
	SetMarkPrice(ctx context.Context, symbol string, price decimal.Decimal)

	GetAllIndexPrice(ctx context.Context) cmap.ConcurrentMap[string, decimal.Decimal]
	GetIndexPrice(ctx context.Context, symbol string) decimal.Decimal
	SetIndexPrice(ctx context.Context, symbol string, price decimal.Decimal)

	GetAllLastPrice(ctx context.Context) cmap.ConcurrentMap[string, decimal.Decimal]
	GetLastPrice(ctx context.Context, symbol string) decimal.Decimal
	SetLastPrice(ctx context.Context, symbol string, price decimal.Decimal)

	SpotURate(ctx context.Context, currency string) decimal.Decimal
	SpotRate(ctx context.Context, base, quote string) decimal.Decimal
}

type MarkPrice struct {
	Symbol     string          `json:"Symbol"`
	MarkPrice  decimal.Decimal `json:"markPrice"`
	IndexPrice decimal.Decimal `json:"indexPrice"`
}

type KLineTicker24hs struct {
	Symbol string          `json:"p"`  // 币对
	Lp     decimal.Decimal `json:"lp"` // 最新价
	Hp     decimal.Decimal `json:"hp"` // 开盘价
	H      decimal.Decimal `json:"h"`  // 最高价
	L      decimal.Decimal `json:"l"`  // 最低价
	V      decimal.Decimal `json:"v"`  // 24小时成交额
	A      decimal.Decimal `json:"a"`  // 24小时成交量
	Cr     decimal.Decimal `json:"cr"` // 24小时涨幅
	Ct     int64           `json:"ct"` // 创建时间
	Ht     int64           `json:"ht"` // 开盘价时间
}
